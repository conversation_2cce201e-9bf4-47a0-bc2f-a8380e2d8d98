name: Docker CI

on:
  workflow_dispatch:
  push:
    branches:
      - main
      - "v*-dev"
    tags: ["*.*.*"]
    paths-ignore:
      - "mkdocs.yml"
      - "docs/**"
      - "**.md"
      - "**.mdx"
      - "**.png"
      - "**.jpg"
      - "**.gif"
      - "Dockerfile.rocm.base"
      - "Dockerfile.dcu.base"
  pull_request:
    branches:
      - main
      - "v*-dev"
    paths:
      - "Dockerfile"
      - "Dockerfile.*"
      - ".github/workflows/docker-ci.yaml"
      - "!Dockerfile.rocm.base"
      - "!Dockerfile.dcu.base"

jobs:
  publish-docker:
    permissions:
      contents: write
      actions: read
      id-token: write
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false
      matrix:
        include:
          #
          # Nvidia CUDA
          #
          - device: cuda
            dockerfile: "Dockerfile"
            platforms: "linux/amd64,linux/arm64"
            tag_suffix: ""
            build_args:
              - "CUDA_VERSION=12.4.1"
              - "CUDA_DEVEL_VERSION=12.6.3"
          - device: cuda
            dockerfile: "Dockerfile"
            platforms: "linux/amd64,linux/arm64"
            tag_suffix: "-cuda12.8"
            build_args:
              - "CUDA_VERSION=12.8.1"
              - "CUDA_DEVEL_VERSION=12.8.1"
          #
          # HIP RoCM
          #
          - device: rocm
            dockerfile: "Dockerfile.rocm"
            platforms: "linux/amd64,linux/arm64"
            tag_suffix: "-rocm"
            build_args:
              - "BUILD_FA=0"
          #
          # Ascend NPU
          #
          - device: npu
            dockerfile: "Dockerfile.npu"
            platforms: "linux/amd64,linux/arm64"
            tag_suffix: "-npu"
            build_args:
              - "CANN_CHIP=910b"
          - device: npu
            dockerfile: "Dockerfile.npu"
            platforms: "linux/amd64,linux/arm64"
            tag_suffix: "-npu-310p"
            build_args:
              - "CANN_CHIP=310p"
          #
          # MooreThreads MUSA
          #
          - device: musa
            dockerfile: "Dockerfile.musa"
            platforms: "linux/amd64"
            tag_suffix: "-musa"
            build_args: []
          #
          # Hygon DCU
          #
          - device: dcu
            dockerfile: "Dockerfile.dcu"
            platforms: "linux/amd64"
            tag_suffix: "-dcu"
            build_args: []
          #
          # CPU
          #
          - device: cpu
            dockerfile: "Dockerfile.cpu"
            platforms: "linux/amd64,linux/arm64"
            tag_suffix: "-cpu"
            build_args: []
          #
          # Iluvatar Corex
          #
          - device: corex
            dockerfile: "Dockerfile.corex"
            platforms: "linux/amd64"
            tag_suffix: "-corex"
            build_args: []

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 1
          persist-credentials: false
      - name: Maximize Docker Build Space
        uses: gpustack/.github/.github/actions/maximize-docker-build-space@main
        with:
          deep-clean: true
          root-reserve-mb: 20480
      - name: Setup QEMU
        uses: docker/setup-qemu-action@v3
        with:
          image: tonistiigi/binfmt:qemu-v9.2.2
          platforms: "arm64"
      - name: Setup Buildx
        uses: docker/setup-buildx-action@v3
      - name: Login DockerHub
        uses: docker/login-action@v3
        if: github.event_name != 'pull_request'
        with:
          username: ${{ secrets.CI_DOCKERHUB_USERNAME }}
          password: ${{ secrets.CI_DOCKERHUB_PASSWORD }}
      - name: Get Metadata
        id: metadata
        uses: docker/metadata-action@v5
        with:
          images: gpustack/gpustack
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=pep440,pattern={{raw}}
            type=pep440,pattern=v{{major}}.{{minor}},enable=${{ startsWith(github.ref, 'refs/tags/') && !contains(github.ref, 'rc') }}
            type=raw,value=latest,enable=${{ startsWith(github.ref, 'refs/tags/') && !contains(github.ref, 'rc') }}
          flavor: |
            latest=false
            suffix=${{ matrix.tag_suffix }}
      - name: Get Build Args
        id: build-args
        env:
          BUILD_ARGS: ${{ join(matrix.build_args, ' ') }}
        run: |
          echo "BUILD_ARGS<<EOF" >> $GITHUB_OUTPUT
          for arg in $BUILD_ARGS; do
            echo "$arg" >> $GITHUB_OUTPUT
          done
          echo "EOF" >> $GITHUB_OUTPUT
      - name: Get Cache Ref
        id: cache-ref
        run: |
          #
          # Use different cache ref for different branches.
          #
          # Examples:
          # CACHE_FROM_REF
          #   - vX.Y.Z                 -> "gpustack/build-cache:gpustack${TAG_SUFFIX}-${VERSION|DEFAULT_BRANCH}"
          #   - PR/PUSH to branch      -> "gpustack/build-cache:gpustack${TAG_SUFFIX}-${BRANCH|DEFUALT_BRANCH}"
          # CACHE_TO_REF
          #   - vX.Y.Z                 -> "gpustack/build-cache:gpustack${TAG_SUFFIX}-${VERSION}"
          #   - PUSH to branch         -> "gpustack/build-cache:gpustack${TAG_SUFFIX}-${BRANCH}"
          #
          # Stories(device cpu):
          # CACHE_FROM_REF
          #   - Release tag v0.7.0rc1      -> gpustack/build-cache:gpustack-cpu-v0.7, if not found, fallback to gpustack/build-cache:gpustack-cpu-main
          #   - Release tag v0.7.0         -> gpustack/build-cache:gpustack-cpu-v0.7
          #   - PR to "main" branch        -> gpustack/build-cache:gpustack-cpu-main
          #   - PR to "v0.7-dev" branch    -> gpustack/build-cache:gpustack-cpu-v0.7, if not found, fallback to gpustack/build-cache:gpustack-cpu-main
          #   - Push to "main" branch      -> gpustack/build-cache:gpustack-cpu-main
          #   - Push to "v0.7-dev" branch  -> gpustack/build-cache:gpustack-cpu-v0.7, if not found, fallback to gpustack/build-cache:gpustack-cpu-main
          # CACHE_TO_REF
          #   - Release tag v0.7.0rc1      -> gpustack/build-cache:gpustack-cpu-v0.7
          #   - Release tag v0.7.0         -> gpustack/build-cache:gpustack-cpu-v0.7
          #   - PR to "main" branch        -> gpustack/build-cache:gpustack-cpu-main
          #   - PR to "v0.7-dev" branch    -> gpustack/build-cache:gpustack-cpu-v0.7
          #   - Push to "main" branch      -> gpustack/build-cache:gpustack-cpu-main
          #   - Push to "v0.7-dev" branch  -> gpustack/build-cache:gpustack-cpu-v0.7
          DEFAULT_BRANCH="main"
          TAG_SUFFIX="${{ matrix.tag_suffix }}"
          if [[ "${GITHUB_REF}" == refs/tags/* ]]; then
            REF="${GITHUB_REF#refs/tags/}"
            IFS="." read -r VERSION_MAJOR VERSION_MINOR VERSION_PATCH <<< "${REF}"
            VERSION="${VERSION_MAJOR}.${VERSION_MINOR}"
            CACHE_FROM_REF="gpustack/build-cache:gpustack${TAG_SUFFIX}-${VERSION}"
            CACHE_TO_REF="${CACHE_FROM_REF}"
          else
            REF="${GITHUB_BASE_REF:-${GITHUB_REF}}"
            BRANCH="${REF#refs/heads/}"
            BRANCH="${BRANCH%-dev}"
            CACHE_FROM_REF="gpustack/build-cache:gpustack${TAG_SUFFIX}-${BRANCH}"
            CACHE_TO_REF="${CACHE_FROM_REF}"
          fi
          if ! docker manifest inspect "${CACHE_FROM_REF}" >/dev/null 2>&1; then
            CACHE_FROM_REF="gpustack/build-cache:gpustack${TAG_SUFFIX}-${DEFAULT_BRANCH}"
          fi
          echo "CACHE_FROM_REF=${CACHE_FROM_REF}" >> $GITHUB_ENV
          echo "CACHE_TO_REF=${CACHE_TO_REF}" >> $GITHUB_ENV
          echo "DEBUG: GITHUB_BASE_REF=${GITHUB_BASE_REF}"
          echo "DEBUG: GITHUB_REF=${GITHUB_REF}"
          echo "DEBUG: TAG_SUFFIX=${TAG_SUFFIX}"
          echo "DEBUG: CACHE_FROM_REF=${CACHE_FROM_REF}"
          echo "DEBUG: CACHE_TO_REF=${CACHE_TO_REF}"
      - name: Package
        uses: docker/build-push-action@v6
        id: package
        with:
          push: ${{ github.event_name != 'pull_request' }}
          file: ${{ github.workspace }}/${{ matrix.dockerfile }}
          context: ${{ github.workspace }}
          platforms: ${{ matrix.platforms }}
          tags: ${{ steps.metadata.outputs.tags }}
          labels: ${{ steps.metadata.outputs.labels }}
          provenance: true
          sbom: true
          build-args: |
            ${{ steps.build-args.outputs.BUILD_ARGS }}
          cache-from: |
            type=registry,ref=${{ env.CACHE_FROM_REF }}
          cache-to: |
            ${{ github.event_name != 'pull_request' && format('type=registry,mode=max,compression=gzip,ref={0},ignore-error=true', env.CACHE_TO_REF) || '' }}
