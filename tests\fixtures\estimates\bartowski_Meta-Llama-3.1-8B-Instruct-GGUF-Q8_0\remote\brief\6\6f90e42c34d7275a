{"header": {"magic": **********, "version": 3, "tensorCount": 292, "metadataKVCount": 33, "metadataKV": [{"key": "general.architecture", "valueType": 8, "value": "llama"}, {"key": "general.type", "valueType": 8, "value": "model"}, {"key": "general.name", "valueType": 8, "value": "Meta Llama 3.1 8B Instruct"}, {"key": "general.finetune", "valueType": 8, "value": "Instruct"}, {"key": "general.basename", "valueType": 8, "value": "Meta-Llama-3.1"}, {"key": "general.size_label", "valueType": 8, "value": "8B"}, {"key": "general.license", "valueType": 8, "value": "llama3.1"}, {"key": "general.tags", "valueType": 9, "value": {"type": 8, "len": 6, "startOffset": 365, "size": 94}}, {"key": "general.languages", "valueType": 9, "value": {"type": 8, "len": 8, "startOffset": 500, "size": 80}}, {"key": "llama.block_count", "valueType": 4, "value": 32}, {"key": "llama.context_length", "valueType": 4, "value": 131072}, {"key": "llama.embedding_length", "valueType": 4, "value": 4096}, {"key": "llama.feed_forward_length", "valueType": 4, "value": 14336}, {"key": "llama.attention.head_count", "valueType": 4, "value": 32}, {"key": "llama.attention.head_count_kv", "valueType": 4, "value": 8}, {"key": "llama.rope.freq_base", "valueType": 6, "value": 500000}, {"key": "llama.attention.layer_norm_rms_epsilon", "valueType": 6, "value": 1e-05}, {"key": "general.file_type", "valueType": 4, "value": 7}, {"key": "llama.vocab_size", "valueType": 4, "value": 128256}, {"key": "llama.rope.dimension_count", "valueType": 4, "value": 128}, {"key": "tokenizer.ggml.model", "valueType": 8, "value": "gpt2"}, {"key": "tokenizer.ggml.pre", "valueType": 8, "value": "llama-bpe"}, {"key": "tokenizer.ggml.tokens", "valueType": 9, "value": {"type": 8, "len": 128256, "startOffset": 1148, "size": 2099452}}, {"key": "tokenizer.ggml.token_type", "valueType": 9, "value": {"type": 5, "len": 128256, "startOffset": 2100649, "size": 513024}}, {"key": "tokenizer.ggml.merges", "valueType": 9, "value": {"type": 8, "len": 280147, "startOffset": 2613718, "size": 5204765}}, {"key": "tokenizer.ggml.bos_token_id", "valueType": 4, "value": 128000}, {"key": "tokenizer.ggml.eos_token_id", "valueType": 4, "value": 128009}, {"key": "tokenizer.chat_template", "valueType": 8, "value": "{{- bos_token }}\n{%- if custom_tools is defined %}\n    {%- set tools = custom_tools %}\n{%- endif %}\n{%- if not tools_in_user_message is defined %}\n    {%- set tools_in_user_message = true %}\n{%- endif %}\n{%- if not date_string is defined %}\n    {%- set date_string = \"26 Jul 2024\" %}\n{%- endif %}\n{%- if not tools is defined %}\n    {%- set tools = none %}\n{%- endif %}\n\n{#- This block extracts the system message, so we can slot it into the right place. #}\n{%- if messages[0]['role'] == 'system' %}\n    {%- set system_message = messages[0]['content']|trim %}\n    {%- set messages = messages[1:] %}\n{%- else %}\n    {%- set system_message = \"\" %}\n{%- endif %}\n\n{#- System message + builtin tools #}\n{{- \"<|start_header_id|>system<|end_header_id|>\\n\\n\" }}\n{%- if builtin_tools is defined or tools is not none %}\n    {{- \"Environment: ipython\\n\" }}\n{%- endif %}\n{%- if builtin_tools is defined %}\n    {{- \"Tools: \" + builtin_tools | reject('equalto', 'code_interpreter') | join(\", \") + \"\\n\\n\"}}\n{%- endif %}\n{{- \"Cutting Knowledge Date: December 2023\\n\" }}\n{{- \"Today Date: \" + date_string + \"\\n\\n\" }}\n{%- if tools is not none and not tools_in_user_message %}\n    {{- \"You have access to the following functions. To call a function, please respond with JSON for a function call.\" }}\n    {{- 'Respond in the format {\"name\": function name, \"parameters\": dictionary of argument name and its value}.' }}\n    {{- \"Do not use variables.\\n\\n\" }}\n    {%- for t in tools %}\n        {{- t | tojson(indent=4) }}\n        {{- \"\\n\\n\" }}\n    {%- endfor %}\n{%- endif %}\n{{- system_message }}\n{{- \"<|eot_id|>\" }}\n\n{#- Custom tools are passed in a user message with some extra guidance #}\n{%- if tools_in_user_message and not tools is none %}\n    {#- Extract the first user message so we can plug it in here #}\n    {%- if messages | length != 0 %}\n        {%- set first_user_message = messages[0]['content']|trim %}\n        {%- set messages = messages[1:] %}\n    {%- else %}\n        {{- raise_exception(\"Cannot put tools in the first user message when there's no first user message!\") }}\n{%- endif %}\n    {{- '<|start_header_id|>user<|end_header_id|>\\n\\n' -}}\n    {{- \"Given the following functions, please respond with a JSON for a function call \" }}\n    {{- \"with its proper arguments that best answers the given prompt.\\n\\n\" }}\n    {{- 'Respond in the format {\"name\": function name, \"parameters\": dictionary of argument name and its value}.' }}\n    {{- \"Do not use variables.\\n\\n\" }}\n    {%- for t in tools %}\n        {{- t | tojson(indent=4) }}\n        {{- \"\\n\\n\" }}\n    {%- endfor %}\n    {{- first_user_message + \"<|eot_id|>\"}}\n{%- endif %}\n\n{%- for message in messages %}\n    {%- if not (message.role == 'ipython' or message.role == 'tool' or 'tool_calls' in message) %}\n        {{- '<|start_header_id|>' + message['role'] + '<|end_header_id|>\\n\\n'+ message['content'] | trim + '<|eot_id|>' }}\n    {%- elif 'tool_calls' in message %}\n        {%- if not message.tool_calls|length == 1 %}\n            {{- raise_exception(\"This model only supports single tool-calls at once!\") }}\n        {%- endif %}\n        {%- set tool_call = message.tool_calls[0].function %}\n        {%- if builtin_tools is defined and tool_call.name in builtin_tools %}\n            {{- '<|start_header_id|>assistant<|end_header_id|>\\n\\n' -}}\n            {{- \"<|python_tag|>\" + tool_call.name + \".call(\" }}\n            {%- for arg_name, arg_val in tool_call.arguments | items %}\n                {{- arg_name + '=\"' + arg_val + '\"' }}\n                {%- if not loop.last %}\n                    {{- \", \" }}\n                {%- endif %}\n                {%- endfor %}\n            {{- \")\" }}\n        {%- else  %}\n            {{- '<|start_header_id|>assistant<|end_header_id|>\\n\\n' -}}\n            {{- '{\"name\": \"' + tool_call.name + '\", ' }}\n            {{- '\"parameters\": ' }}\n            {{- tool_call.arguments | tojson }}\n            {{- \"}\" }}\n        {%- endif %}\n        {%- if builtin_tools is defined %}\n            {#- This means we're in ipython mode #}\n            {{- \"<|eom_id|>\" }}\n        {%- else %}\n            {{- \"<|eot_id|>\" }}\n        {%- endif %}\n    {%- elif message.role == \"tool\" or message.role == \"ipython\" %}\n        {{- \"<|start_header_id|>ipython<|end_header_id|>\\n\\n\" }}\n        {%- if message.content is mapping or message.content is iterable %}\n            {{- message.content | tojson }}\n        {%- else %}\n            {{- message.content }}\n        {%- endif %}\n        {{- \"<|eot_id|>\" }}\n    {%- endif %}\n{%- endfor %}\n{%- if add_generation_prompt %}\n    {{- '<|start_header_id|>assistant<|end_header_id|>\\n\\n' }}\n{%- endif %}"}, {"key": "general.quantization_version", "valueType": 4, "value": 2}, {"key": "quantize.imatrix.file", "valueType": 8, "value": "/models_out/Meta-Llama-3.1-8B-Instruct-GGUF/Meta-Llama-3.1-8B-Instruct.imatrix"}, {"key": "quantize.imatrix.dataset", "valueType": 8, "value": "/training_dir/calibration_datav3.txt"}, {"key": "quantize.imatrix.entries_count", "valueType": 5, "value": 224}, {"key": "quantize.imatrix.chunks_count", "valueType": 5, "value": 125}]}, "tensorInfos": [{"name": "rope_freqs.weight", "nDimensions": 1, "dimensions": [64], "type": 0, "offset": 0, "startOffset": 7823572}, {"name": "token_embd.weight", "nDimensions": 2, "dimensions": [4096, 128256], "type": 8, "offset": 256, "startOffset": 7823621}, {"name": "blk.0.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 558170368, "startOffset": 7823678}, {"name": "blk.0.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 558186752, "startOffset": 7823732}, {"name": "blk.0.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 620577024, "startOffset": 7823793}, {"name": "blk.0.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 682967296, "startOffset": 7823854}, {"name": "blk.0.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 745357568, "startOffset": 7823913}, {"name": "blk.0.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 745373952, "startOffset": 7823966}, {"name": "blk.0.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 749830400, "startOffset": 7824025}, {"name": "blk.0.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 767656192, "startOffset": 7824089}, {"name": "blk.0.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 785481984, "startOffset": 7824148}, {"name": "blk.1.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 789938432, "startOffset": 7824207}, {"name": "blk.1.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 789954816, "startOffset": 7824261}, {"name": "blk.1.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 852345088, "startOffset": 7824322}, {"name": "blk.1.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 914735360, "startOffset": 7824383}, {"name": "blk.1.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 977125632, "startOffset": 7824442}, {"name": "blk.1.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 977142016, "startOffset": 7824495}, {"name": "blk.1.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 981598464, "startOffset": 7824554}, {"name": "blk.1.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 999424256, "startOffset": 7824618}, {"name": "blk.1.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 1017250048, "startOffset": 7824677}, {"name": "blk.2.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 1021706496, "startOffset": 7824736}, {"name": "blk.2.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 1021722880, "startOffset": 7824790}, {"name": "blk.2.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 1084113152, "startOffset": 7824851}, {"name": "blk.2.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 1146503424, "startOffset": 7824912}, {"name": "blk.2.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 1208893696, "startOffset": 7824971}, {"name": "blk.2.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 1208910080, "startOffset": 7825024}, {"name": "blk.2.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 1213366528, "startOffset": 7825083}, {"name": "blk.2.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 1231192320, "startOffset": 7825147}, {"name": "blk.2.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 1249018112, "startOffset": 7825206}, {"name": "blk.3.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 1253474560, "startOffset": 7825265}, {"name": "blk.3.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 1253490944, "startOffset": 7825319}, {"name": "blk.3.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 1315881216, "startOffset": 7825380}, {"name": "blk.3.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 1378271488, "startOffset": 7825441}, {"name": "blk.3.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 1440661760, "startOffset": 7825500}, {"name": "blk.3.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 1440678144, "startOffset": 7825553}, {"name": "blk.3.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 1445134592, "startOffset": 7825612}, {"name": "blk.3.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 1462960384, "startOffset": 7825676}, {"name": "blk.3.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 1480786176, "startOffset": 7825735}, {"name": "blk.4.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 1485242624, "startOffset": 7825794}, {"name": "blk.4.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 1485259008, "startOffset": 7825848}, {"name": "blk.4.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 1547649280, "startOffset": 7825909}, {"name": "blk.4.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 1610039552, "startOffset": 7825970}, {"name": "blk.4.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 1672429824, "startOffset": 7826029}, {"name": "blk.4.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 1672446208, "startOffset": 7826082}, {"name": "blk.4.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 1676902656, "startOffset": 7826141}, {"name": "blk.4.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 1694728448, "startOffset": 7826205}, {"name": "blk.4.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 1712554240, "startOffset": 7826264}, {"name": "blk.5.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 1717010688, "startOffset": 7826323}, {"name": "blk.5.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 1717027072, "startOffset": 7826377}, {"name": "blk.5.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 1779417344, "startOffset": 7826438}, {"name": "blk.5.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 1841807616, "startOffset": 7826499}, {"name": "blk.5.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 1904197888, "startOffset": 7826558}, {"name": "blk.5.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 1904214272, "startOffset": 7826611}, {"name": "blk.5.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 1908670720, "startOffset": 7826670}, {"name": "blk.5.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 1926496512, "startOffset": 7826734}, {"name": "blk.5.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 1944322304, "startOffset": 7826793}, {"name": "blk.6.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 1948778752, "startOffset": 7826852}, {"name": "blk.6.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 1948795136, "startOffset": 7826906}, {"name": "blk.6.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 2011185408, "startOffset": 7826967}, {"name": "blk.6.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 2073575680, "startOffset": 7827028}, {"name": "blk.6.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 2135965952, "startOffset": 7827087}, {"name": "blk.6.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 2135982336, "startOffset": 7827140}, {"name": "blk.6.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 2140438784, "startOffset": 7827199}, {"name": "blk.6.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 2158264576, "startOffset": 7827263}, {"name": "blk.6.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 2176090368, "startOffset": 7827322}, {"name": "blk.7.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 2180546816, "startOffset": 7827381}, {"name": "blk.7.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 2180563200, "startOffset": 7827435}, {"name": "blk.7.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 2242953472, "startOffset": 7827496}, {"name": "blk.7.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 2305343744, "startOffset": 7827557}, {"name": "blk.7.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 2367734016, "startOffset": 7827616}, {"name": "blk.7.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 2367750400, "startOffset": 7827669}, {"name": "blk.7.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 2372206848, "startOffset": 7827728}, {"name": "blk.7.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 2390032640, "startOffset": 7827792}, {"name": "blk.7.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 2407858432, "startOffset": 7827851}, {"name": "blk.8.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 2412314880, "startOffset": 7827910}, {"name": "blk.8.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 2412331264, "startOffset": 7827964}, {"name": "blk.8.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 2474721536, "startOffset": 7828025}, {"name": "blk.8.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 2537111808, "startOffset": 7828086}, {"name": "blk.8.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 2599502080, "startOffset": 7828145}, {"name": "blk.8.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 2599518464, "startOffset": 7828198}, {"name": "blk.8.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 2603974912, "startOffset": 7828257}, {"name": "blk.8.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 2621800704, "startOffset": 7828321}, {"name": "blk.8.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 2639626496, "startOffset": 7828380}, {"name": "blk.10.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 2644082944, "startOffset": 7828439}, {"name": "blk.10.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 2644099328, "startOffset": 7828494}, {"name": "blk.10.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 2706489600, "startOffset": 7828556}, {"name": "blk.10.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 2768879872, "startOffset": 7828618}, {"name": "blk.10.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 2831270144, "startOffset": 7828678}, {"name": "blk.10.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 2831286528, "startOffset": 7828732}, {"name": "blk.10.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 2835742976, "startOffset": 7828792}, {"name": "blk.10.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 2853568768, "startOffset": 7828857}, {"name": "blk.10.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 2871394560, "startOffset": 7828917}, {"name": "blk.11.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 2875851008, "startOffset": 7828977}, {"name": "blk.11.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 2875867392, "startOffset": 7829032}, {"name": "blk.11.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 2938257664, "startOffset": 7829094}, {"name": "blk.11.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 3000647936, "startOffset": 7829156}, {"name": "blk.11.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 3063038208, "startOffset": 7829216}, {"name": "blk.11.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 3063054592, "startOffset": 7829270}, {"name": "blk.11.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 3067511040, "startOffset": 7829330}, {"name": "blk.11.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 3085336832, "startOffset": 7829395}, {"name": "blk.11.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 3103162624, "startOffset": 7829455}, {"name": "blk.12.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 3107619072, "startOffset": 7829515}, {"name": "blk.12.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 3107635456, "startOffset": 7829570}, {"name": "blk.12.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 3170025728, "startOffset": 7829632}, {"name": "blk.12.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 3232416000, "startOffset": 7829694}, {"name": "blk.12.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 3294806272, "startOffset": 7829754}, {"name": "blk.12.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 3294822656, "startOffset": 7829808}, {"name": "blk.12.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 3299279104, "startOffset": 7829868}, {"name": "blk.12.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 3317104896, "startOffset": 7829933}, {"name": "blk.12.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 3334930688, "startOffset": 7829993}, {"name": "blk.13.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 3339387136, "startOffset": 7830053}, {"name": "blk.13.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 3339403520, "startOffset": 7830108}, {"name": "blk.13.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 3401793792, "startOffset": 7830170}, {"name": "blk.13.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 3464184064, "startOffset": 7830232}, {"name": "blk.13.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 3526574336, "startOffset": 7830292}, {"name": "blk.13.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 3526590720, "startOffset": 7830346}, {"name": "blk.13.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 3531047168, "startOffset": 7830406}, {"name": "blk.13.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 3548872960, "startOffset": 7830471}, {"name": "blk.13.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 3566698752, "startOffset": 7830531}, {"name": "blk.14.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 3571155200, "startOffset": 7830591}, {"name": "blk.14.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 3571171584, "startOffset": 7830646}, {"name": "blk.14.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 3633561856, "startOffset": 7830708}, {"name": "blk.14.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 3695952128, "startOffset": 7830770}, {"name": "blk.14.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 3758342400, "startOffset": 7830830}, {"name": "blk.14.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 3758358784, "startOffset": 7830884}, {"name": "blk.14.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 3762815232, "startOffset": 7830944}, {"name": "blk.14.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 3780641024, "startOffset": 7831009}, {"name": "blk.14.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 3798466816, "startOffset": 7831069}, {"name": "blk.15.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 3802923264, "startOffset": 7831129}, {"name": "blk.15.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 3802939648, "startOffset": 7831184}, {"name": "blk.15.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 3865329920, "startOffset": 7831246}, {"name": "blk.15.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 3927720192, "startOffset": 7831308}, {"name": "blk.15.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 3990110464, "startOffset": 7831368}, {"name": "blk.15.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 3990126848, "startOffset": 7831422}, {"name": "blk.15.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 3994583296, "startOffset": 7831482}, {"name": "blk.15.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 4012409088, "startOffset": 7831547}, {"name": "blk.15.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 4030234880, "startOffset": 7831607}, {"name": "blk.16.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 4034691328, "startOffset": 7831667}, {"name": "blk.16.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 4034707712, "startOffset": 7831722}, {"name": "blk.16.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 4097097984, "startOffset": 7831784}, {"name": "blk.16.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 4159488256, "startOffset": 7831846}, {"name": "blk.16.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 4221878528, "startOffset": 7831906}, {"name": "blk.16.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 4221894912, "startOffset": 7831960}, {"name": "blk.16.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 4226351360, "startOffset": 7832020}, {"name": "blk.16.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 4244177152, "startOffset": 7832085}, {"name": "blk.16.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 4262002944, "startOffset": 7832145}, {"name": "blk.17.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 4266459392, "startOffset": 7832205}, {"name": "blk.17.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 4266475776, "startOffset": 7832260}, {"name": "blk.17.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 4328866048, "startOffset": 7832322}, {"name": "blk.17.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 4391256320, "startOffset": 7832384}, {"name": "blk.17.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 4453646592, "startOffset": 7832444}, {"name": "blk.17.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 4453662976, "startOffset": 7832498}, {"name": "blk.17.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 4458119424, "startOffset": 7832558}, {"name": "blk.17.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 4475945216, "startOffset": 7832623}, {"name": "blk.17.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 4493771008, "startOffset": 7832683}, {"name": "blk.18.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 4498227456, "startOffset": 7832743}, {"name": "blk.18.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 4498243840, "startOffset": 7832798}, {"name": "blk.18.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 4560634112, "startOffset": 7832860}, {"name": "blk.18.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 4623024384, "startOffset": 7832922}, {"name": "blk.18.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 4685414656, "startOffset": 7832982}, {"name": "blk.18.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 4685431040, "startOffset": 7833036}, {"name": "blk.18.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 4689887488, "startOffset": 7833096}, {"name": "blk.18.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 4707713280, "startOffset": 7833161}, {"name": "blk.18.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 4725539072, "startOffset": 7833221}, {"name": "blk.19.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 4729995520, "startOffset": 7833281}, {"name": "blk.19.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 4730011904, "startOffset": 7833336}, {"name": "blk.19.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 4792402176, "startOffset": 7833398}, {"name": "blk.19.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 4854792448, "startOffset": 7833460}, {"name": "blk.19.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 4917182720, "startOffset": 7833520}, {"name": "blk.19.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 4917199104, "startOffset": 7833574}, {"name": "blk.19.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 4921655552, "startOffset": 7833634}, {"name": "blk.19.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 4939481344, "startOffset": 7833699}, {"name": "blk.19.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 4957307136, "startOffset": 7833759}, {"name": "blk.20.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 4961763584, "startOffset": 7833819}, {"name": "blk.20.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 5024153856, "startOffset": 7833881}, {"name": "blk.20.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 5028610304, "startOffset": 7833941}, {"name": "blk.20.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 5046436096, "startOffset": 7834006}, {"name": "blk.20.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 5064261888, "startOffset": 7834066}, {"name": "blk.9.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 5068718336, "startOffset": 7834126}, {"name": "blk.9.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 5068734720, "startOffset": 7834180}, {"name": "blk.9.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 5131124992, "startOffset": 7834241}, {"name": "blk.9.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 5193515264, "startOffset": 7834302}, {"name": "blk.9.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 5255905536, "startOffset": 7834361}, {"name": "blk.9.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 5255921920, "startOffset": 7834414}, {"name": "blk.9.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 5260378368, "startOffset": 7834473}, {"name": "blk.9.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 5278204160, "startOffset": 7834537}, {"name": "blk.9.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 5296029952, "startOffset": 7834596}, {"name": "blk.20.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 5300486400, "startOffset": 7834655}, {"name": "blk.20.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 5300502784, "startOffset": 7834710}, {"name": "blk.20.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 5362893056, "startOffset": 7834772}, {"name": "blk.20.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 5425283328, "startOffset": 7834832}, {"name": "blk.21.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 5425299712, "startOffset": 7834886}, {"name": "blk.21.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 5425316096, "startOffset": 7834941}, {"name": "blk.21.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 5487706368, "startOffset": 7835003}, {"name": "blk.21.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 5550096640, "startOffset": 7835065}, {"name": "blk.21.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 5612486912, "startOffset": 7835125}, {"name": "blk.21.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 5612503296, "startOffset": 7835179}, {"name": "blk.21.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 5616959744, "startOffset": 7835239}, {"name": "blk.21.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 5634785536, "startOffset": 7835304}, {"name": "blk.21.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 5652611328, "startOffset": 7835364}, {"name": "blk.22.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 5657067776, "startOffset": 7835424}, {"name": "blk.22.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 5657084160, "startOffset": 7835479}, {"name": "blk.22.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 5719474432, "startOffset": 7835541}, {"name": "blk.22.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 5781864704, "startOffset": 7835603}, {"name": "blk.22.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 5844254976, "startOffset": 7835663}, {"name": "blk.22.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 5844271360, "startOffset": 7835717}, {"name": "blk.22.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 5848727808, "startOffset": 7835777}, {"name": "blk.22.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 5866553600, "startOffset": 7835842}, {"name": "blk.22.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 5884379392, "startOffset": 7835902}, {"name": "blk.23.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 5888835840, "startOffset": 7835962}, {"name": "blk.23.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 5888852224, "startOffset": 7836017}, {"name": "blk.23.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 5951242496, "startOffset": 7836079}, {"name": "blk.23.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 6013632768, "startOffset": 7836141}, {"name": "blk.23.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 6076023040, "startOffset": 7836201}, {"name": "blk.23.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 6076039424, "startOffset": 7836255}, {"name": "blk.23.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 6080495872, "startOffset": 7836315}, {"name": "blk.23.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 6098321664, "startOffset": 7836380}, {"name": "blk.23.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 6116147456, "startOffset": 7836440}, {"name": "blk.24.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 6120603904, "startOffset": 7836500}, {"name": "blk.24.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 6120620288, "startOffset": 7836555}, {"name": "blk.24.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 6183010560, "startOffset": 7836617}, {"name": "blk.24.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 6245400832, "startOffset": 7836679}, {"name": "blk.24.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 6307791104, "startOffset": 7836739}, {"name": "blk.24.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 6307807488, "startOffset": 7836793}, {"name": "blk.24.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 6312263936, "startOffset": 7836853}, {"name": "blk.24.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 6330089728, "startOffset": 7836918}, {"name": "blk.24.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 6347915520, "startOffset": 7836978}, {"name": "blk.25.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 6352371968, "startOffset": 7837038}, {"name": "blk.25.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 6352388352, "startOffset": 7837093}, {"name": "blk.25.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 6414778624, "startOffset": 7837155}, {"name": "blk.25.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 6477168896, "startOffset": 7837217}, {"name": "blk.25.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 6539559168, "startOffset": 7837277}, {"name": "blk.25.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 6539575552, "startOffset": 7837331}, {"name": "blk.25.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 6544032000, "startOffset": 7837391}, {"name": "blk.25.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 6561857792, "startOffset": 7837456}, {"name": "blk.25.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 6579683584, "startOffset": 7837516}, {"name": "blk.26.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 6584140032, "startOffset": 7837576}, {"name": "blk.26.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 6584156416, "startOffset": 7837631}, {"name": "blk.26.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 6646546688, "startOffset": 7837693}, {"name": "blk.26.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 6708936960, "startOffset": 7837755}, {"name": "blk.26.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 6771327232, "startOffset": 7837815}, {"name": "blk.26.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 6771343616, "startOffset": 7837869}, {"name": "blk.26.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 6775800064, "startOffset": 7837929}, {"name": "blk.26.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 6793625856, "startOffset": 7837994}, {"name": "blk.26.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 6811451648, "startOffset": 7838054}, {"name": "blk.27.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 6815908096, "startOffset": 7838114}, {"name": "blk.27.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 6815924480, "startOffset": 7838169}, {"name": "blk.27.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 6878314752, "startOffset": 7838231}, {"name": "blk.27.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 6940705024, "startOffset": 7838293}, {"name": "blk.27.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 7003095296, "startOffset": 7838353}, {"name": "blk.27.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 7003111680, "startOffset": 7838407}, {"name": "blk.27.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 7007568128, "startOffset": 7838467}, {"name": "blk.27.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 7025393920, "startOffset": 7838532}, {"name": "blk.27.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 7043219712, "startOffset": 7838592}, {"name": "blk.28.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 7047676160, "startOffset": 7838652}, {"name": "blk.28.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 7047692544, "startOffset": 7838707}, {"name": "blk.28.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 7110082816, "startOffset": 7838769}, {"name": "blk.28.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 7172473088, "startOffset": 7838831}, {"name": "blk.28.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 7234863360, "startOffset": 7838891}, {"name": "blk.28.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 7234879744, "startOffset": 7838945}, {"name": "blk.28.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 7239336192, "startOffset": 7839005}, {"name": "blk.28.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 7257161984, "startOffset": 7839070}, {"name": "blk.28.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 7274987776, "startOffset": 7839130}, {"name": "blk.29.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 7279444224, "startOffset": 7839190}, {"name": "blk.29.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 7279460608, "startOffset": 7839245}, {"name": "blk.29.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 7341850880, "startOffset": 7839307}, {"name": "blk.29.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 7404241152, "startOffset": 7839369}, {"name": "blk.29.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 7466631424, "startOffset": 7839429}, {"name": "blk.29.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 7466647808, "startOffset": 7839483}, {"name": "blk.29.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 7471104256, "startOffset": 7839543}, {"name": "blk.29.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 7488930048, "startOffset": 7839608}, {"name": "blk.29.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 7506755840, "startOffset": 7839668}, {"name": "blk.30.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 7511212288, "startOffset": 7839728}, {"name": "blk.30.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 7511228672, "startOffset": 7839783}, {"name": "blk.30.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 7573618944, "startOffset": 7839845}, {"name": "blk.30.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 7636009216, "startOffset": 7839907}, {"name": "blk.30.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 7698399488, "startOffset": 7839967}, {"name": "blk.30.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 7698415872, "startOffset": 7840021}, {"name": "blk.30.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 7702872320, "startOffset": 7840081}, {"name": "blk.30.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 7720698112, "startOffset": 7840146}, {"name": "blk.30.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 7738523904, "startOffset": 7840206}, {"name": "blk.31.ffn_gate.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 7742980352, "startOffset": 7840266}, {"name": "blk.31.ffn_up.weight", "nDimensions": 2, "dimensions": [4096, 14336], "type": 8, "offset": 7805370624, "startOffset": 7840328}, {"name": "blk.31.attn_k.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 7867760896, "startOffset": 7840388}, {"name": "blk.31.attn_output.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 7872217344, "startOffset": 7840448}, {"name": "blk.31.attn_q.weight", "nDimensions": 2, "dimensions": [4096, 4096], "type": 8, "offset": 7890043136, "startOffset": 7840513}, {"name": "blk.31.attn_v.weight", "nDimensions": 2, "dimensions": [4096, 1024], "type": 8, "offset": 7907868928, "startOffset": 7840573}, {"name": "output.weight", "nDimensions": 2, "dimensions": [4096, 128256], "type": 8, "offset": 7912325376, "startOffset": 7840633}, {"name": "blk.31.attn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 8470495488, "startOffset": 7840686}, {"name": "blk.31.ffn_down.weight", "nDimensions": 2, "dimensions": [14336, 4096], "type": 8, "offset": 8470511872, "startOffset": 7840741}, {"name": "blk.31.ffn_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 8532902144, "startOffset": 7840803}, {"name": "output_norm.weight", "nDimensions": 1, "dimensions": [4096], "type": 0, "offset": 8532918528, "startOffset": 7840857}], "padding": 21, "splitPaddings": [21], "tensorDataStartOffset": 7840928, "splitTensorDataStartOffsets": [7840928], "size": 8540775840, "splitSizes": [8540775840], "modelSize": 8532934912, "splitModelSizes": [8532934912], "modelParameters": 8030261312, "modelBitsPerWeight": 8.500779320093937}