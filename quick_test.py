#!/usr/bin/env python3
"""
快速测试脚本 - 验证核心功能
"""

import os
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 添加模块路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def quick_test():
    """快速测试核心功能"""
    print("🚀 GPUStack 日志轮转快速测试")
    print("-" * 50)
    
    try:
        from gpustack.worker.daily_rotating_logger import DailyRotatingLogFile
        print("✅ 模块导入成功")
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    # 创建测试目录
    test_dir = Path("quick_test_logs")
    test_dir.mkdir(exist_ok=True)
    
    try:
        # 创建logger（短检查间隔）
        logger = DailyRotatingLogFile(str(test_dir), check_interval=5)
        print("✅ Logger创建成功")
        
        # 测试写入
        logger.write("测试消息1\n")
        logger.write("测试消息2\n")
        print("✅ 日志写入成功")
        
        # 检查文件
        current_file = logger.get_current_log_file()
        if os.path.exists(current_file):
            with open(current_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"✅ 文件内容: {len(content)} 字符")
        
        # 测试轮转
        tomorrow = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
        original_get_date = logger._get_current_date
        logger._get_current_date = lambda: tomorrow
        logger._rotate_if_needed()
        logger.write("轮转后的消息\n")
        logger._get_current_date = original_get_date
        
        # 检查文件数量
        log_files = list(test_dir.glob("*.log"))
        print(f"✅ 生成文件数: {len(log_files)}")
        
        # 关闭
        logger.close()
        print("✅ Logger关闭成功")
        
        # 清理
        import shutil
        shutil.rmtree(test_dir)
        print("✅ 测试目录清理完成")
        
        print("\n🎉 快速测试全部通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    quick_test()
