name: CI

on:
  workflow_dispatch:
  push:
    branches:
      - main
      - "v*-dev"
    tags: ["*.*.*"]
    paths-ignore:
      - "mkdocs.yml"
      - "docs/**"
      - "**.md"
      - "**.mdx"
      - "**.png"
      - "**.jpg"
      - "**.gif"

jobs:
  linux-macos:
    timeout-minutes: 20
    runs-on: ubuntu-latest

    strategy:
      matrix:
        python-version: ["3.10", "3.11", "3.12"]
        os: [linux, darwin]
    steps:
      - uses: actions/checkout@v4
        name: Checkout code

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/pypoetry/artifacts
            ~/.cache/pypoetry/repository
            ~/.cache/pypoetry/virtualenvs
          key: ${{ runner.os }}-poetry-${{ matrix.python-version }}-${{ hashFiles('**/poetry.lock') }}
          restore-keys: |
            ${{ runner.os }}-poetry-${{ matrix.python-version }}-

      - name: Run CI build
        run: |
          make ci
        env:
          BUILD_OS: ${{ matrix.os }}

      - name: Upload wheel as artifact
        if: matrix.python-version == '3.11' && matrix.os == 'linux'
        uses: actions/upload-artifact@v4
        with:
          name: gpustack-dist
          path: dist/*.whl
          retention-days: 5

      - name: Release GitHub Assets
        uses: softprops/action-gh-release@v2
        if: startsWith(github.ref, 'refs/tags/') && matrix.python-version == '3.11' && matrix.os == 'linux'
        with:
          # Draft for official releases to prepare and review release notes before publishing
          draft: ${{ !contains(github.ref, 'rc') }}
          fail_on_unmatched_files: true
          prerelease: ${{ contains(github.ref, 'rc') }}
          files: dist/*.whl

      - name: Publish to PyPI
        if: startsWith(github.ref, 'refs/tags/') && matrix.python-version == '3.11' && matrix.os == 'linux'
        env:
          TWINE_USERNAME: __token__
          TWINE_PASSWORD: ${{ secrets.CI_PYPI_API_TOKEN }}
          PUBLISH_SOURCE: ${{ matrix.os == 'linux' && '1' || '' }}
        run: |
          make publish-pypi

  windows-amd64:
    timeout-minutes: 20
    runs-on: windows-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11", "3.12"]

    steps:
      - uses: actions/checkout@v4
        name: Checkout code

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: CI
        shell: powershell
        run: |
          make ci
