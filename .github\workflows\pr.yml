name: PR Checking

on:
  pull_request:
    branches:
      - main
      - "v*-dev"
    paths-ignore:
      - "mkdocs.yml"
      - "docs/**"
      - "**.md"
      - "**.mdx"
      - "**.png"
      - "**.jpg"
      - "**.gif"

jobs:
  linux-macos:
    timeout-minutes: 20
    runs-on: ubuntu-latest

    strategy:
      matrix:
        python-version: ["3.12"]
        os: [ubuntu-latest]
    steps:
      - uses: actions/checkout@v4
        name: Checkout code

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/pypoetry/artifacts
            ~/.cache/pypoetry/repository
            ~/.cache/pypoetry/virtualenvs
          key: ${{ runner.os }}-poetry-${{ matrix.python-version }}-${{ hashFiles('**/poetry.lock') }}
          restore-keys: |
            ${{ runner.os }}-poetry-${{ matrix.python-version }}-

      - name: Run CI build
        run: |
          make ci

  windows:
    timeout-minutes: 20
    runs-on: windows-latest
    strategy:
      matrix:
        python-version: ["3.12"]

    steps:
      - uses: actions/checkout@v4
        name: Checkout code

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: CI
        shell: powershell
        run: |
          make ci
