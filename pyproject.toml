[tool.poetry]
name = "gpustack"
version = "v0.0.0"
description = "GPUStack"
authors = ["GPUStack Authors <<EMAIL>>"]
readme = "README.md"
include = ["*/third_party/config/**/*","*/ui/**/*", "gpustack.pth"]

[tool.poetry.dependencies]
python = ">=3.10,<3.13"
fastapi = "^0.115.0"
sqlmodel = "^0.0.18"
pydantic = "^2.11.5"
pydantic-settings = "^2.2.1"
setproctitle = "^1.3.3"
inflection = "^0.5.1"
openai = "^1.31.1"

prometheus-client = "^0.20.0"
colorama = "^0.4.6"
psutil = "^7.0.0"
requests = "^2.32.3"
huggingface-hub = ">=0.32.0"
attrs = "^24.2.0"
aiosqlite = "^0.20.0"
sqlalchemy = {extras = ["asyncio"], version = "^2.0.38"}
pyjwt = "^2.8.0"
argon2-cffi = "^23.1.0"
python-multipart = "^0.0.17"
dataclasses-json = "^0.6.7"
apscheduler = "^3.10.4"
asyncpg = "^0.29.0"
httpx = {extras = ["socks"], version = ">=0.27.0"}
alembic = "^1.13.2"
importlib-resources = "^6.4.0"
pytest-asyncio = "^0.23.7"
truststore = "^0.9.1"
cryptography = "^43.0.0"
tzdata = "^2024.1"
modelscope = "^1.28"
ray = {version = "2.43.0", extras = ["default"]}
vllm = {version = "0.9.2", optional = true}
mistral_common = {version = "^1.4.3", optional = true, extras = ["opencv"]}
uvicorn = "^0.32.0"
transformers = "^4.51.3"
fastapi-cdn-host = "^0.8.0"
wmi = { version="^1.5.1", markers = "platform_system == 'Windows'" }
pywin32 = { version="^308", markers = "platform_system == 'Windows'" }
packaging = "^24.1"
psycopg2-binary = "^2.9.10"
vox-box = {version = "0.0.19", optional = true}

tenacity = "^9.0.0"
aiocache = "^0.12.3"
aiofiles = "^23.2.1"
aiohttp = "^3.11.2"
bitsandbytes = {version = "^0.45.2", optional = true}
python-json-logger = "^3.3.0"
hf-transfer = "^0.1.9"
asyncmy = "^0.2.10"
pymysql = "^1.1.1"

pyarrow = "*"
pandas = "*"
blobfile = "^3.0.0"
timm = {version = "^1.0.15", optional = true}
aiolimiter = "^1.2.1"
[tool.poetry.group.dev.dependencies]
coverage = {extras = ["toml"], version = "^7.5.1"}
flake8 = "^7.0.0"
flake8-bugbear = "^24.4.26"
black = "^24.4.2"
pytest = "^8.2.0"
openapi-python-client = ">=0.20.0"
httpx-aiohttp = "^0.1.4"

deptry = "^0.16.1"
pre-commit = "^3.7.1"
mkdocs = "^1.6.0"
mkdocs-material = "^9.5.27"
shellcheck-py = "^********"
twine = "^5.1.1"
mike = "^2.1.3"
mkdocs-glightbox = "^0.4.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py310']
skip-string-normalization = true
force-exclude = '''
/(
    .*/migrations/.*
)/
'''

[tool.poetry.extras]
vllm = ["vllm", "mistral_common", "bitsandbytes", "timm"]
audio = ["vox-box"]
all = ["vllm", "mistral_common", "bitsandbytes", "timm", "vox-box"]

[tool.poetry.scripts]
gpustack = "gpustack.main:main"
