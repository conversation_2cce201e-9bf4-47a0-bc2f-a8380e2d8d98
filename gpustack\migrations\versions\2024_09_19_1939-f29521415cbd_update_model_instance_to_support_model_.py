"""update model, instance to support model scope source

Revision ID: f29521415cbd
Revises: 6dcb3a50da19
Create Date: 2024-09-19 19:39:07.470611

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
import gpustack


# revision identifiers, used by Alembic.
revision: str = 'f29521415cbd'
down_revision: Union[str, None] = '6dcb3a50da19'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('model_instances') as batch_op:
        batch_op.add_column(sa.Column('model_scope_model_id', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
        batch_op.add_column(sa.Column('model_scope_file_path', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    with op.batch_alter_table('models') as batch_op:
        batch_op.add_column(sa.Column('distributable', sa.<PERSON>(), nullable=True))
        batch_op.add_column(sa.Column('model_scope_model_id', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
        batch_op.add_column(sa.Column('model_scope_file_path', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
        batch_op.add_column(sa.Column('backend', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
        batch_op.add_column(sa.Column('backend_parameters', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('models') as batch_op:
        batch_op.drop_column('model_scope_file_path')
        batch_op.drop_column('model_scope_model_id')
        batch_op.drop_column('backend_parameters')
        batch_op.drop_column('backend')
        batch_op.drop_column('distributable')
    with op.batch_alter_table('model_instances') as batch_op:
        batch_op.drop_column('model_scope_file_path')
        batch_op.drop_column('model_scope_model_id')
    # ### end Alembic commands ###
