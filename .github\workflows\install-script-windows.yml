name: Windows Install Script Testing

on:
  push:
    branches:
      - main
      - "v*-dev"
    paths:
      - "install.ps1"
      - ".github/workflows/install-script-windows.yml"
  pull_request:
    branches:
      - main
      - "v*-dev"
    paths:
      - "install.ps1"
      - ".github/workflows/install-script-windows.yml"
  workflow_dispatch: {}
  schedule:
    - cron: "0 0 * * *" # every day at 00:00 UTC

jobs:
  windows-amd64-build:
    timeout-minutes: 20
    runs-on: windows-latest
    strategy:
      matrix:
        arch: [X64]
    steps:
      - uses: actions/checkout@v4
        name: Checkout code

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: CI
        shell: powershell
        run: |
          make build

      - name: Upload wheel as artifact
        uses: actions/upload-artifact@v4
        with:
          name: windows-amd64-dist
          path: dist/*.whl
  windows-amd64-install-test:
    timeout-minutes: 20
    name: Test Install
    needs: windows-amd64-build
    runs-on: windows-latest
    strategy:
      matrix:
        arch: [X64]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Download wheel artifact
        uses: actions/download-artifact@v4
        with:
          name: windows-amd64-dist
          path: ./dist

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Run install script
        shell: powershell
        run: |
          $env:whlPackageName=Get-ChildItem -Path dist -Filter "*.whl" -File | ForEach-Object { $_.Name }
          Write-Host "whlPackageName: $env:whlPackageName"
          $env:INSTALL_PACKAGE_SPEC = [System.IO.Path]::Combine("dist", $env:whlPackageName)   
          Write-Host "INSTALL_PACKAGE_SPEC: $env:INSTALL_PACKAGE_SPEC"   
          Write-Host "AppData $env:APPDATA"

          # Use port 8080 since 80 is occupied by the System
          ./install.ps1 -ServerPort 8080

          Write-Host "Checking if the server is ready..."
          $uri = "http://127.0.0.1:8080/readyz"
          $maxRetries = 5
          $retryDelaySeconds = 2
          $responseCode = 500
          $lastError = $null

          for ($i = 0; $i -lt $maxRetries; $i++) {
              try {
                  $response = Invoke-WebRequest -Uri $uri -ErrorAction Stop
                  $responseCode = $response.StatusCode
                  if ($responseCode -eq 200) {
                      Write-Host "Server response status code: $($response.StatusCode)"
                      Write-Host "Server responded with status code 200 on attempt $($i + 1)."
                      break
                  }
              } catch {
                  Write-Host "Request failed, retrying（$($i + 1) times...）"
                  $lastError = $_.Exception.Message
                  Start-Sleep -Seconds $retryDelaySeconds
              }
          }

          if ($responseCode -ne 200) {
              Write-Host "All retry attempts failed. Last error: $lastError"
          }
