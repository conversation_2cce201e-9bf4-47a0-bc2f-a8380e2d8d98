site_name: GPUStack
site_url: https://docs.gpustack.ai
theme:
  name: material
  logo: assets/logo-white.png
  favicon: assets/logo.png
  custom_dir: docs/overrides
  palette:
    - media: "(prefers-color-scheme: light)"
      scheme: default
      primary: custom
      accent: orange
      toggle:
        icon: material/lightbulb
        name: Switch to dark mode
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      primary: light blue
      accent: orange
      toggle:
        icon: material/lightbulb-outline
        name: Switch to light mode
  features:
    - search.suggest
    - search.highlight
    - content.tabs.link
    - navigation.indexes
    - content.tooltips
    - navigation.path
    - content.code.annotate
    - content.code.copy
    - content.code.select
    - content.action.view
    - content.action.edit
  icon:
    repo: fontawesome/brands/github-alt
  language: en
plugins:
  - glightbox
  - search
extra:
  generator: false
  version:
    provider: mike
extra_javascript:
  - javascripts/katex.js
  - https://unpkg.com/katex@0/dist/katex.min.js
  - https://unpkg.com/katex@0/dist/contrib/auto-render.min.js
extra_css:
  - stylesheets/extra.css
  - https://unpkg.com/katex@0/dist/katex.min.css
markdown_extensions:
  - attr_list
  - md_in_html
  - pymdownx.highlight
  - pymdownx.superfences
  - pymdownx.tabbed:
      alternate_style: true
  - admonition
  - sane_lists
  - pymdownx.details
  - pymdownx.tilde
  - pymdownx.betterem
  - pymdownx.caret
  - pymdownx.inlinehilite
  - pymdownx.tasklist:
      custom_checkbox: true
  - pymdownx.arithmatex:
      generic: true
repo_url: https://github.com/gpustack/gpustack
edit_uri: edit/main/docs/
nav:
  - Overview: overview.md
  - Quickstart: quickstart.md
  - Installation:
      - Installation Requirements: installation/installation-requirements.md
      - NVIDIA CUDA:
          - Online Installation: installation/nvidia-cuda/online-installation.md
          - Air-Gapped Installation: installation/nvidia-cuda/air-gapped-installation.md
      - AMD ROCm:
          - Online Installation: installation/amd-rocm/online-installation.md
          - Air-Gapped Installation: installation/amd-rocm/air-gapped-installation.md
      - Apple Metal: installation/apple-metal-installation.md
      - Ascend CANN:
          - Online Installation: installation/ascend-cann/online-installation.md
          - Air-Gapped Installation: installation/ascend-cann/air-gapped-installation.md
      - Hygon DTK:
          - Online Installation: installation/hygon-dtk/online-installation.md
          - Air-Gapped Installation: installation/hygon-dtk/air-gapped-installation.md
      - Moore Threads MUSA:
          - Online Installation: installation/moorethreads-musa/online-installation.md
          - Air-Gapped Installation: installation/moorethreads-musa/air-gapped-installation.md
      - Iluvatar Corex:
          - Online Installation: installation/iluvatar-corex/online-installation.md
          - Air-Gapped Installation: installation/iluvatar-corex/air-gapped-installation.md
      - Cambricon MLU:
          - Online Installation: installation/cambricon-mlu/online-installation.md
      - CPU:
          - Online Installation: installation/cpu/online-installation.md
          - Air-Gapped Installation: installation/cpu/air-gapped-installation.md
      - Desktop Installer: installation/desktop-installer.md
      - Installation Script: installation/installation-script.md
      - Uninstallation: installation/uninstallation.md
  - Upgrade: upgrade.md
  - Migration: migration.md
  - User Guide:
      - Playground:
          - Playgound: user-guide/playground/index.md
          - Chat: user-guide/playground/chat.md
          - Image: user-guide/playground/image.md
          - Audio: user-guide/playground/audio.md
          - Embedding: user-guide/playground/embedding.md
          - Rerank: user-guide/playground/rerank.md
      - Model Deployment Management: user-guide/model-deployment-management.md
      - Model Catalog: user-guide/model-catalog.md
      - Model File management: user-guide/model-file-management.md
      - API Key Management: user-guide/api-key-management.md
      - User Management: user-guide/user-management.md
      - Inference Backends: user-guide/inference-backends.md
      - Pinned Backend Versions: user-guide/pinned-backend-versions.md
      - Compatibility Check: user-guide/compatibility-check.md
      - OpenAI Compatible APIs: user-guide/openai-compatible-apis.md
      - Image Generation APIs: user-guide/image-generation-apis.md
      - Rerank API: user-guide/rerank-api.md
      - Desktop Setup: user-guide/desktop-setup.md
  - Using Models:
      - Using Large Language Models: using-models/using-large-language-models.md
      - Using Vision Language Models: using-models/using-vision-language-models.md
      - Using Embedding Models: using-models/using-embedding-models.md
      - Using Reranker Models: using-models/using-reranker-models.md
      - Using Image Generation Models: using-models/using-image-generation-models.md
      - Recommended Parameters for Image Generation Models: using-models/recommended-parameters-for-image-generation-models.md
      - Editing Images: using-models/editing-images.md
      - Using Audio Models: using-models/using-audio-models.md
  - Tutorials:
      - Running DeepSeek R1 671B with Distributed vLLM: tutorials/running-deepseek-r1-671b-with-distributed-vllm.md
      - Running DeepSeek R1 671B with Distributed Ascend Mindie: tutorials/running-deepseek-r1-671b-with-distributed-ascend-mindie.md
      - Performing Distributed Inference Across Workers (llama-box): tutorials/performing-distributed-inference-across-workers-llama-box.md
      - Inference On CPUs: tutorials/inference-on-cpus.md
      - Inference with Tool Calling: tutorials/inference-with-tool-calling.md
      - Running on Copilot+ PCs with Snapdragon X: tutorials/running-on-copilot-plus-pcs-with-snapdragon-x.md
  - Integrations:
      - OpenAI Compatible APIs: integrations/openai-compatible-apis.md
      - Integrate with Dify: integrations/integrate-with-dify.md
      - Integrate with RAGFlow: integrations/integrate-with-ragflow.md
  - Architecture: architecture.md
  - Scheduler: scheduler.md
  - Troubleshooting: troubleshooting.md
  - FAQ: faq.md
  - API Reference: api-reference.md
  - CLI Reference:
      - Start: cli-reference/start.md
      - Chat: cli-reference/chat.md
      - Draw: cli-reference/draw.md
      - Download Tools: cli-reference/download-tools.md
