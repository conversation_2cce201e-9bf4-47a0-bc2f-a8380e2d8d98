# GPUStack 日志轮转系统测试

## 测试脚本说明

### 🧪 统一测试脚本 (`test_log_system_unified.py`)

全面的日志轮转系统测试，包含以下测试模块：

1. **基本日志记录** - 测试基础写入功能
2. **日期轮转机制** - 测试跨日期文件轮转
3. **线程安全性** - 测试多线程并发写入
4. **性能测试** - 测试大量写入的性能
5. **集成测试** - 测试与RedirectStdoutStderr的集成
6. **清理和关闭** - 测试资源清理

**运行方法：**
```bash
python test_log_system_unified.py
```

**特点：**
- 自动创建和清理测试环境
- 详细的测试报告
- 模拟日期变化测试轮转
- 多线程并发测试
- 性能基准测试

### 🚀 快速测试脚本 (`quick_test.py`)

轻量级的快速验证脚本，用于验证核心功能是否正常。

**运行方法：**
```bash
python quick_test.py
```

**特点：**
- 快速执行（< 10秒）
- 验证核心功能
- 自动清理
- 适合CI/CD集成

## 测试覆盖的功能

### 核心功能
- ✅ 日志文件创建和写入
- ✅ 按日期自动轮转
- ✅ 文件以追加模式打开
- ✅ 线程安全的并发写入
- ✅ 后台线程定期检查

### 性能特性
- ✅ 写入操作无日期检查开销
- ✅ 高吞吐量日志写入
- ✅ 低延迟响应时间
- ✅ 内存使用优化

### 集成特性
- ✅ 与RedirectStdoutStderr集成
- ✅ 支持模型实例日志重定向
- ✅ 兼容GPUStack日志架构
- ✅ 向后兼容旧日志格式

## 日志轮转机制

### 工作原理
1. **后台线程检查**: 每5分钟检查一次日期变化
2. **即时写入**: write()方法直接写入，无检查开销
3. **自动轮转**: 检测到日期变化时自动创建新文件
4. **追加模式**: 所有文件以追加模式打开，确保不覆盖

### 文件结构
```
logs/serve/
├── model_name_1/
│   ├── replica_1/
│   │   ├── 2025-07-31.log
│   │   ├── 2025-08-01.log
│   │   └── 2025-08-02.log
│   └── replica_2/
│       ├── 2025-07-31.log
│       └── 2025-08-01.log
└── instance_mapping.txt
```

### 性能优化
- **线程分离**: 日期检查与写入操作分离
- **无锁写入**: 大部分写入操作无需等待锁
- **批量刷新**: 行缓冲模式确保实时性
- **最小开销**: 每次写入仅需文件I/O操作

## 故障排除

### 常见问题

1. **导入错误**
   ```
   ImportError: No module named 'gpustack'
   ```
   - 确保在GPUStack项目根目录运行
   - 检查Python路径设置

2. **权限错误**
   ```
   PermissionError: [Errno 13] Permission denied
   ```
   - 检查日志目录写入权限
   - 确保没有其他进程占用日志文件

3. **线程未启动**
   ```
   ⚠️ 后台检查线程未运行
   ```
   - 检查线程创建是否成功
   - 验证daemon线程设置

### 调试模式

在测试脚本中设置调试信息：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 部署验证

### 生产环境检查清单

- [ ] 基本写入功能正常
- [ ] 日期轮转机制工作
- [ ] 多实例并发写入无冲突
- [ ] 文件权限设置正确
- [ ] 磁盘空间充足
- [ ] 日志查看API正常

### 监控指标

- 日志写入延迟 (< 1ms)
- 文件轮转成功率 (100%)
- 线程运行状态 (健康)
- 磁盘使用量 (正常)

## 更新日志

### v1.0 (2025-07-31)
- 实现基于线程的日志轮转机制
- 优化写入性能，移除每次的日期检查
- 支持并发安全的多线程写入
- 集成RedirectStdoutStderr支持
- 添加完整的测试套件
