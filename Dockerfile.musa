ARG UBUNTU_VERSION=22.04
ARG MUSA_VERSION=rc4.0.1
ARG BASE_MUSA_DEV_CONTAINER=mthreads/musa:${MUSA_VERSION}-mudnn-devel-ubuntu${UBUNTU_VERSION}
ARG BASE_MUSA_RUN_CONTAINER=mthreads/musa:${MUSA_VERSION}-mudnn-runtime-ubuntu${UBUNTU_VERSION}

FROM ${BASE_MUSA_DEV_CONTAINER} AS build

ARG TARGETPLATFORM
ENV DEBIAN_FRONTEND=noninteractive

RUN apt-get update && apt-get install -y \
    git \
    curl

COPY . /workspace/gpustack
RUN cd /workspace/gpustack && make build

FROM ${BASE_MUSA_RUN_CONTAINER} AS runtime

RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    wget \
    tzdata \
    iproute2 \
    tini \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

COPY --from=build /workspace/gpustack/dist/*.whl /dist/
RUN pip install /dist/*.whl && \
    pip cache purge && \
    rm -rf /dist

RUN gpustack download-tools

ENTRYPOINT [ "tini", "--", "gpustack", "start" ]
