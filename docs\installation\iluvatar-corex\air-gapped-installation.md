# Air-Gapped Installation

You can install GPUStack in an air-gapped environment. An air-gapped environment refers to a setup where GPUStack will be installed offline.

The following methods are available for installing GPUStack in an air-gapped environment:

| OS    | Arch  | Supported methods                           |
| ----- | ----- | ------------------------------------------- |
| Linux | AMD64 | [Docker Installation](#docker-installation) |

## Supported backends

- [x] vLLM

## Docker Installation

### Prerequisites

- [Driver for MR-V100 MR-V50 BI-V100 BI-V150](https://support.iluvatar.com/#/ProductLine?id=2)

Check if the driver is installed:

```bash
ixsmi
```

- [Docker](https://support.iluvatar.com/#/ProductLine?id=2)
- [Corex Container Toolkits](https://support.iluvatar.com/#/ProductLine?id=2)

### Run GPUStack

When running GPUStack with <PERSON><PERSON>, it works out of the box in an air-gapped environment as long as the Docker images are available. To do this, follow these steps:

1. Pull GPUStack docker image in an online environment:

```bash
docker pull gpustack/gpustack:latest-corex
```

If your online environment differs from the air-gapped environment in terms of OS or arch, specify the OS and arch of the air-gapped environment when pulling the image:

```bash
docker pull --platform linux/amd64 gpustack/gpustack:latest-corex
```

2. Publish docker image to a private registry or load it directly in the air-gapped environment.
3. Refer to the [Docker Installation](./online-installation.md#docker-installation) guide to run GPUStack using Docker.
