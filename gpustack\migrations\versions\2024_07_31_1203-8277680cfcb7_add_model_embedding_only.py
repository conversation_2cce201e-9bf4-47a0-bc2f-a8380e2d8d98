"""add model embedding only

Revision ID: 8277680cfcb7
Revises: 1dd9fa5b38ff
Create Date: 2024-07-31 12:03:41.325109

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
import gpustack


# revision identifiers, used by Alembic.
revision: str = '8277680cfcb7'
down_revision: Union[str, None] = '1dd9fa5b38ff'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('models') as batch_op:
        # The default is False, but is not valid in SQLite which uses interger for boolean. Use a generic representation.
        batch_op.add_column(sa.Column('embedding_only', sa.<PERSON>(), nullable=False, server_default="0"))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('models') as batch_op:
        batch_op.drop_column('embedding_only')
    # ### end Alembic commands ###
