ARG BASE_IMAGE=gpustack/rocm-base:rocm6.2_ubuntu20.04_py3.10_pytorch2.6.0_vllm0.6.6.post1

FROM $BASE_IMAGE

ARG TARGETPLATFORM
ENV DEBIAN_FRONTEND=noninteractive

RUN apt-get update && apt-get install -y \
    python3-venv \
    tzdata \
    iproute2 \
    build-essential \
    tini \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

COPY . /workspace/gpustack
RUN cd /workspace/gpustack && make build

# Install GPUStack
RUN python3 -m pip install pipx \
    && USER_BASE_BIN=$(python3 -m site --user-base)/bin \
    && export PATH="$USER_BASE_BIN:$PATH" \
    && pipx ensurepath --force \
    && WHEEL_PACKAGE="$(ls /workspace/gpustack/dist/*.whl)[audio]" \
    && pipx install $WHEEL_PACKAGE \
    && rm -rf /workspace/gpustack

RUN /root/.local/bin/gpustack download-tools

RUN ln -s $(which vllm) /root/.local/share/pipx/venvs/gpustack/bin/vllm

ENTRYPOINT [ "tini", "--", "/root/.local/bin/gpustack", "start" ]
