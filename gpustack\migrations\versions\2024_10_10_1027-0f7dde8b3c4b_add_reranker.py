"""add reranker

Revision ID: 0f7dde8b3c4b
Revises: f29521415cbd
Create Date: 2024-10-10 10:27:48.495536

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
import gpustack


# revision identifiers, used by Alembic.
revision: str = '0f7dde8b3c4b'
down_revision: Union[str, None] = 'f29521415cbd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('models') as batch_op:
        batch_op.add_column(sa.Column('reranker', sa.<PERSON>(), nullable=False, server_default="0"))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('models') as batch_op:
        batch_op.drop_column('reranker')
    # ### end Alembic commands ###
