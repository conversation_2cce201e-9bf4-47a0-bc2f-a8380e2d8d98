FROM image.sourcefind.cn:5000/dcu/admin/base/vllm:0.8.5-ubuntu22.04-dtk25.04.1-py3.10

ENV PATH="/root/.local/bin:$PATH"
ENV DEBIAN_FRONTEND=noninteractive

RUN apt-get update && apt-get install -y \
    python3-venv \
    tzdata \
    iproute2 \
    iputils-ping \
    build-essential \
    tini \
    && rm -rf /var/lib/apt/lists/*

RUN --mount=type=bind,target=/workspace/gpustack,rw \
    cd /workspace/gpustack && make build \
    && python3 -m pip install pipx \
    && pipx ensurepath --force \
    && WHEEL_PACKAGE="$(ls /workspace/gpustack/dist/*.whl)[audio]" \
    && echo $WHEEL_PACKAGE \
    && pipx install $WHEEL_PACKAGE \
    && pip cache purge

RUN gpustack download-tools --device dcu \
    && ln -s $(which vllm) /root/.local/share/pipx/venvs/gpustack/bin/vllm

ENTRYPOINT [ "tini", "--", "gpustack", "start" ]
