name: Linux and MaxOS Install Script Testing

on:
  push:
    branches:
      - main
      - "v*-dev"
    paths:
      - "install.sh"
      - ".github/workflows/install-script.yml"
  pull_request:
    branches:
      - main
      - "v*-dev"
    paths:
      - "install.sh"
      - ".github/workflows/install-script.yml"
  workflow_dispatch: {}
  schedule:
    - cron: "0 0 * * *" # every day at 00:00 UTC
jobs:
  linux-macos-build:
    timeout-minutes: 20
    name: Build Wheel
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            arch: X64
          - os: macos-latest
            arch: ARM64
    steps:
      - uses: actions/checkout@v4
        name: Checkout code

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Build
        run: |
          make build

      - name: Upload wheel as artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.os }}-dist
          path: dist/*.whl

  linux-macos-install-test:
    timeout-minutes: 20
    name: Test Install
    needs: linux-macos-build
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            arch: X64
          - os: macos-latest
            arch: ARM64
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: setup-python
        id: cp311
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      # latest macos runner uses python3.13 which is not supported yet.
      - name: set pipx default python
        run: echo "PIPX_DEFAULT_PYTHON=$(which python)" >> $GITHUB_ENV

      - name: Download wheel artifact
        uses: actions/download-artifact@v4
        with:
          name: ${{ matrix.os }}-dist
          path: ./dist

      - name: Run install script
        continue-on-error: true
        run: |
          export INSTALL_PACKAGE_SPEC=$(ls dist/*.whl)
          cat install.sh | sh -

      - name: Verify installation
        run: |
          gpustack version
          curl --fail --retry 5 --retry-delay 2 --retry-all-errors http://127.0.0.1/readyz
