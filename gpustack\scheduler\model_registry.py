# Synced with https://github.com/vllm-project/vllm/blob/v0.9.1/vllm/model_executor/models/registry.py
# Update these when the builtin vLLM is updated
vllm_supported_embedding_architectures = [
    "BertModel",
    "GteModel",
    "GteNewModel",
    "Gemma2Model",
    "GPT2ForSequenceClassification",
    "MistralModel",
    "ModernBertModel",
    "NomicBertModel",
    "LlamaModel",
    "Qwen2Model",
    "RobertaModel",
    "RobertaForMaskedLM",
    "XLMRobertaModel",
]

vllm_supported_reranker_architectures = [
    "BertForSequenceClassification",
    "RobertaForSequenceClassification",
    "XLMRobertaForSequenceClassification",
    "ModernBertForSequenceClassification",
    "Qwen2ForSequenceClassification",
    "Qwen3ForSequenceClassification",
]

vllm_supported_llm_architectures = [
    # Text generation models
    "AquilaModel",
    "AquilaForCausalLM",
    "ArcticForCausalLM",
    "MiniMaxText01ForCausalLM",
    "MiniMaxM1ForCausalLM",
    "BaiChuanForCausalLM",
    "BaichuanForCausalLM",
    "BambaForCausalLM",
    "BloomForCausalLM",
    "ChatGLMModel",
    "ChatGLMForConditionalGeneration",
    "CohereForCausalLM",
    "Cohere2ForCausalLM",
    "DbrxForCausalLM",
    "DeciLMForCausalLM",
    "DeepseekForCausalLM",
    "DeepseekV2ForCausalLM",
    "DeepseekV3ForCausalLM",
    "Dots1ForCausalLM",
    "Ernie4_5_ForCausalLM",
    "Ernie4_5_MoeForCausalLM",
    "ExaoneForCausalLM",
    "FalconForCausalLM",
    "Fairseq2LlamaForCausalLM",
    "GemmaForCausalLM",
    "Gemma2ForCausalLM",
    "Gemma3ForCausalLM",
    "Gemma3nForConditionalGeneration",
    "GlmForCausalLM",
    "Glm4ForCausalLM",
    "GPT2LMHeadModel",
    "GPTBigCodeForCausalLM",
    "GPTJForCausalLM",
    "GPTNeoXForCausalLM",
    "GraniteForCausalLM",
    "GraniteMoeForCausalLM",
    "GraniteMoeHybridForCausalLM",
    "GraniteMoeSharedForCausalLM",
    "GritLM",
    "Grok1ModelForCausalLM",
    "HunYuanMoEV1ForCausalLM",
    "InternLMForCausalLM",
    "InternLM2ForCausalLM",
    "InternLM2VEForCausalLM",
    "InternLM3ForCausalLM",
    "JAISLMHeadModel",
    "JambaForCausalLM",
    "LlamaForCausalLM",
    "LLaMAForCausalLM",
    "MambaForCausalLM",
    "FalconMambaForCausalLM",
    "FalconH1ForCausalLM",
    "Mamba2ForCausalLM",
    "MiniCPMForCausalLM",
    "MiniCPM3ForCausalLM",
    "MistralForCausalLM",
    "MixtralForCausalLM",
    "QuantMixtralForCausalLM",
    "MptForCausalLM",
    "MPTForCausalLM",
    "MiMoForCausalLM",
    "NemotronForCausalLM",
    "OlmoForCausalLM",
    "Olmo2ForCausalLM",
    "OlmoeForCausalLM",
    "OPTForCausalLM",
    "OrionForCausalLM",
    "PersimmonForCausalLM",
    "PhiForCausalLM",
    "Phi3ForCausalLM",
    "Phi3SmallForCausalLM",
    "PhiMoEForCausalLM",
    "Plamo2ForCausalLM",
    "QWenLMHeadModel",
    "Qwen2ForCausalLM",
    "Qwen2MoeForCausalLM",
    "Qwen3ForCausalLM",
    "Qwen3MoeForCausalLM",
    "RWForCausalLM",
    "StableLMEpochForCausalLM",
    "StableLmForCausalLM",
    "Starcoder2ForCausalLM",
    "SolarForCausalLM",
    "TeleChat2ForCausalLM",
    "TeleFLMForCausalLM",
    "XverseForCausalLM",
    "Zamba2ForCausalLM",
    "BartModel",
    "BartForConditionalGeneration",
    # Multimodal models
    "AriaForConditionalGeneration",
    "AyaVisionForConditionalGeneration",
    "Blip2ForConditionalGeneration",
    "ChameleonForConditionalGeneration",
    "DeepseekVLV2ForCausalLM",
    "FuyuForCausalLM",
    "Gemma3ForConditionalGeneration",
    "GLM4VForCausalLM",
    "Glm4vForConditionalGeneration",
    "GraniteSpeechForConditionalGeneration",
    "H2OVLChatModel",
    "InternVLChatModel",
    "Idefics3ForConditionalGeneration",
    "SmolVLMForConditionalGeneration",
    "KeyeForConditionalGeneration",
    "KimiVLForConditionalGeneration",
    "LlavaForConditionalGeneration",
    "LlavaNextForConditionalGeneration",
    "LlavaNextVideoForConditionalGeneration",
    "LlavaOnevisionForConditionalGeneration",
    "MantisForConditionalGeneration",
    "MiniMaxVL01ForConditionalGeneration",
    "MiniCPMO",
    "MiniCPMV",
    "Mistral3ForConditionalGeneration",
    "MolmoForCausalLM",
    "NVLM_D",
    "Ovis",
    "PaliGemmaForConditionalGeneration",
    "Phi3VForCausalLM",
    "PixtralForConditionalGeneration",
    "QwenVLForConditionalGeneration",
    "Qwen2VLForConditionalGeneration",
    "Qwen2_5_VLForConditionalGeneration",
    "Qwen2AudioForConditionalGeneration",
    "Qwen2_5OmniModel",
    "Qwen2_5OmniForConditionalGeneration",
    "UltravoxModel",
    "Phi4MMForCausalLM",
    "TarsierForConditionalGeneration",
    "Tarsier2ForConditionalGeneration",
    "Florence2ForConditionalGeneration",
    "MllamaForConditionalGeneration",
    "Llama4ForConditionalGeneration",
    "SkyworkR1VChatModel",
    "WhisperForConditionalGeneration",
]
