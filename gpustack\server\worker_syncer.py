
import asyncio
import logging
import time
from sqlmodel.ext.asyncio.session import AsyncSession
from gpustack.schemas.workers import Worker, WorkerStateEnum
from gpustack.server.db import get_engine
from gpustack.server.services import WorkerService
from gpustack.utils.network import is_url_reachable
from gpustack.utils import platform

# 导入沐曦显卡增强模块
try:
    from gpustack.worker.muxi_state_manager import (
        enhance_worker_health_check,
        attempt_worker_recovery,
        muxi_state_manager
    )
    MUXI_SUPPORT_AVAILABLE = True
except ImportError:
    MUXI_SUPPORT_AVAILABLE = False

logger = logging.getLogger(__name__)


class WorkerSyncer:
    """
    WorkerSyncer syncs worker status periodically.
    """

    def __init__(
        self, interval=15, worker_offline_timeout=100, worker_unreachable_timeout=10
    ):
        self._engine = get_engine()
        self._interval = interval
        self._worker_offline_timeout = worker_offline_timeout
        self._worker_unreachable_timeout = worker_unreachable_timeout
        # 沐曦增强检查的频率限制
        self._last_muxi_check_time = {}  # worker_id -> last_check_time
        self._muxi_check_interval = 30  # 30秒最小间隔

    async def start(self):
        while True:
            await asyncio.sleep(self._interval)
            try:
                await self._sync_workers_connectivity()
            except Exception as e:
                logger.error(f"Failed to sync workers: {e}")

    async def _sync_workers_connectivity(self):
        """
        Mark offline workers to not_ready state.
        """
        async with AsyncSession(self._engine) as session:
            workers = await Worker.all(session)
            if not workers:
                return

            tasks = [
                self._check_worker_connectivity(worker, session) for worker in workers
            ]
            results = await asyncio.gather(*tasks)

            should_update_workers = []
            state_to_worker_name = {
                WorkerStateEnum.NOT_READY: [],
                WorkerStateEnum.UNREACHABLE: [],
                WorkerStateEnum.READY: [],
            }
            for worker in results:
                if worker:
                    should_update_workers.append(worker)
                    state_to_worker_name[worker.state].append(worker.name)

            for worker in should_update_workers:
                await WorkerService(session).update(worker)

            for state, worker_names in state_to_worker_name.items():
                if worker_names:
                    logger.debug(f"Marked worker {', '.join(worker_names)} as {state}")

    async def _check_worker_connectivity(self, worker: Worker, session: AsyncSession):
        original_worker_unreachable = worker.unreachable
        original_worker_state = worker.state
        original_worker_state_message = worker.state_message

        unreachable = not await self.is_worker_reachable(worker)
        worker = await Worker.one_by_id(session, worker.id)
        worker.unreachable = unreachable
        worker.compute_state(self._worker_offline_timeout)

        if (
            original_worker_unreachable != worker.unreachable
            or original_worker_state != worker.state
            or original_worker_state_message != worker.state_message
        ):
            return worker

        return None

    async def is_worker_reachable(
        self,
        worker: Worker,
    ) -> bool:
        # 标准健康检查
        healthz_url = f"http://{worker.ip}:{worker.port}/healthz"
        reachable = await is_url_reachable(
            healthz_url,
            self._worker_unreachable_timeout,
        )

        # 沐曦显卡环境的增强检查（带频率限制）
        if (
            not reachable
            and MUXI_SUPPORT_AVAILABLE
            and platform.device() == platform.DeviceTypeEnum.MUXI.value
        ):
            current_time = time.time()
            last_check_time = self._last_muxi_check_time.get(worker.id, 0)

            if current_time - last_check_time >= self._muxi_check_interval:
                logger.warning(f"Worker {worker.name} 标准健康检查失败，启动沐曦增强检查")
                self._last_muxi_check_time[worker.id] = current_time

                try:
                    # 执行增强健康检查
                    health_result = await enhance_worker_health_check(worker)

                    if not health_result['healthy']:
                        logger.error(
                            f"Worker {worker.name} 沐曦增强检查失败: {health_result['message']}"
                        )

                        # 记录详细问题信息
                        if 'details' in health_result:
                            details = health_result['details']
                            if 'issues' in details:
                                logger.error(f"检测到的问题: {details['issues']}")
                            if 'recommendations' in details:
                                logger.info(f"建议的解决方案: {details['recommendations']}")

                        # 尝试恢复
                        logger.info(f"尝试恢复Worker {worker.name}")
                        recovery_success = await attempt_worker_recovery(worker)

                        if recovery_success:
                            logger.info(f"Worker {worker.name} 恢复成功，重新进行健康检查")
                            # 重新检查标准健康检查
                            await asyncio.sleep(2)  # 等待恢复生效
                            reachable = await is_url_reachable(
                                healthz_url,
                                self._worker_unreachable_timeout,
                            )
                            if reachable:
                                logger.info(f"Worker {worker.name} 恢复后健康检查通过")
                            else:
                                logger.warning(f"Worker {worker.name} 恢复后健康检查仍失败")
                        else:
                            logger.warning(f"Worker {worker.name} 自动恢复失败")
                    else:
                        logger.info(f"Worker {worker.name} 沐曦增强检查通过，可能是网络问题")

                except Exception as e:
                    logger.error(f"Worker {worker.name} 沐曦增强检查过程中出错: {e}")
            else:
                logger.debug(f"Worker {worker.name} 沐曦增强检查跳过（频率限制）")

        return reachable
